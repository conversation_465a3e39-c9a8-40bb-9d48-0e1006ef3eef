﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.MANAGE
{
    public partial class MANAGE_PLAN_OUT : AvalonDock.DocumentContent
    {
        int intPlanID = 0;

        Model.MANAGE_TYPE mMANAGE_TYPE = null;

        DataRowView[] listDataRowView = null;

        string STORAGE_AREA_TYPE = string.Empty;

        /// <summary>
        /// 构造函数
        /// </summary>
        public MANAGE_PLAN_OUT(int PLAN_ID, string MANAGE_TYPE_CODE, string AREA_TYPE)
        {
            InitializeComponent();

           

            intPlanID = PLAN_ID;

            STORAGE_AREA_TYPE = AREA_TYPE;

            this.mMANAGE_TYPE = (Model.MANAGE_TYPE)MainApp.I_BaseService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE).RequestObject;

        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.InitManagePosotion();

            this.PLAN_LIST_Bind(intPlanID);
        }

        private void InitManagePosotion()
        {
            this.ucManagePosition.U_InitControl(mMANAGE_TYPE.MANAGE_TYPE_ID);
        }


        private void Register_DataTable_Event()
        {
            //当输入列值改变后验证数据是否合法
            foreach (TabItem tabItem in this.ucStorageGroup.tabSplitProperty.Items)
            {
                if (tabItem.HasContent)
                {
                    DataTable tableSource = (tabItem.Content as UC.ucCommonDataGrid).U_DataSource.Table;

                    tableSource.ColumnChanged -= new DataColumnChangeEventHandler(table_ColumnChanged);
                    tableSource.ColumnChanged += new DataColumnChangeEventHandler(table_ColumnChanged);
                }
            }
        }


        /// <summary>
        /// 表单数据校验
        /// </summary>
        private void table_ColumnChanged(object sender, DataColumnChangeEventArgs e)
        {
            bool bResult = true;
            string sResult = string.Empty;
            //判断列
            switch (e.Column.ColumnName)
            {
                case "MANAGE_LIST_QUANTITY":
                    bResult = ( Convert.ToDecimal(e.ProposedValue) <= Convert.ToDecimal(e.Row["STORAGE_LIST_QUANTITY"]));

                    sResult = string.Format("出库数量不能大于库存数量!");
                    e.Row.ClearErrors();
                    if (bResult)
                    {
                        e.Row.SetColumnError(e.Column, null);
                    }
                    else
                    {
                        e.Row.SetColumnError(e.Column, sResult);
                    }
                    break;
            }


        }


        private void PLAN_LIST_Bind(int PLAN_ID)
        {
            //数据源属性
            this.gridPlanList.U_Clear();
            this.gridPlanList.U_WindowName = this.GetType().Name;
            this.gridPlanList.U_TableName = "V_PLAN_LIST";
            this.gridPlanList.U_XmlTableName = "V_PLAN_LIST";
            this.gridPlanList.U_Fields = "*";
            this.gridPlanList.U_Where = string.Format("PLAN_ID ={0}", PLAN_ID);
            this.gridPlanList.U_OrderField = "PLAN_LIST_ID";

            this.gridPlanList.U_AllowChecked = true;
            this.gridPlanList.U_AllowOperatData = false;
            this.gridPlanList.U_AllowShowPage = false;

            //拆分列属性
            this.gridPlanList.U_SplitPropertyType = "GOODS_TYPE";
            this.gridPlanList.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.gridPlanList.U_SplitPropertyColumn = "GOODS_PROPERTY";
            this.gridPlanList.U_SplitGroupHeader = "GOODS_TYPE.GOODS_TYPE_NAME";

            //明细属性
            //this.gridPlanList.U_DetailTableName = "PLAN_DETAIL";
            //this.gridPlanList.U_DetailRelatvieColumn = "PLAN_LIST_ID";

            try
            {
                this.gridPlanList.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        private void StorageListBind(string QueryWhere)
        {
            this.ucStorageGroup.U_WindowName = this.GetType().Name;
            this.ucStorageGroup.U_TableName = "V_STORAGE_LIST";
            this.ucStorageGroup.U_XmlTableName = "V_STORAGE_LIST";
            //this.ucStorageGroup.U_AppendFieldStyles = this.GetColumnDescriptionList();
            this.ucStorageGroup.U_TotalColumnName = "STORAGE_LIST_QUANTITY";
            this.ucStorageGroup.U_OrderField = "STORAGE_LIST_ID";
            this.ucStorageGroup.U_Where = QueryWhere;
            //this.ucStorageGroup.U_Where = string.Format("STOCK_BARCODE='{0}' AND AREA_TYPE= '{1}'", txtStockBarCode.Text, Enum.AREA_TYPE.XuNiKu.ToString());
            this.ucStorageGroup.U_AllowOperatData = false;
            this.ucStorageGroup.U_AllowChecked = true;
            this.ucStorageGroup.U_AllowShowPage = true;

            //拆分列属性
            this.ucStorageGroup.U_SplitPropertyType = "GOODS_TYPE";
            this.ucStorageGroup.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.ucStorageGroup.U_SplitGroupHeader = "GOODS_TYPE.GOODS_TYPE_NAME";
            this.ucStorageGroup.U_SplitPropertyColumn = "GOODS_PROPERTY";

            this.ucStorageGroup.U_InitControl();

            this.Register_DataTable_Event();
        }

        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnConfirm":
                        this.CreateTask();
                        break;
                    case "btnQuery":
                        this.Query();
                        break;
                }
            }
        }

        /// <summary>
        /// 创建输送任务
        /// </summary>
        private void CreateTask()
        {
            bool boolResult = true;

            string strResult = string.Empty;

            try
            {
                if (!this.ucManagePosition.U_CHECK_WAREHOUSE())
                    return;


                listDataRowView = this.ucStorageGroup.U_GetCheckedDataRows();

                if (listDataRowView.Length == 0)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.SelectCount);
                    return;
                }

                this.ucStorageGroup.U_EndCurrentEdit();

                //判断数据是否合法
                if (this.ucStorageGroup.U_DataSource.HasErrors)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.Data);
                    return;
                }

                //xcjt add 2016-12-30
                foreach (DataRowView drv in listDataRowView)
                {
                    if (drv["MANAGE_LIST_QUANTITY"].ToString() == "0" || drv["MANAGE_LIST_QUANTITY"].ToString() == "0.0")
                    {
                        drv["MANAGE_LIST_QUANTITY"] = drv["STORAGE_LIST_QUANTITY"];
                    }
                }

                //获得列表
                IDictionary<string, Model.MANAGE_MAIN> dicStack = this.GetPalletKeyValuePair(listDataRowView);

                //判断是否选中移库记录
                if (dicStack.Count == 0)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.CheckStack);
                    return;
                }


                DataRowView[] drvPLAN_LIST = this.gridPlanList.U_GetCheckedDataRows();

                if (drvPLAN_LIST.Length != 1)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.SelectCount);
                    return;
                }

                List<Model.PLAN_LIST> listPLAN_LIST = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.PLAN_LIST>(drvPLAN_LIST, null);

                //List<Model.MANAGE_LIST> listMANAGE_LIST= new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.MANAGE_LIST>(listDataRowView.Cast<DataRowView>().ToArray(), null);

                if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.ConfirmCreateTask, this.Title) == Sid.Windows.Controls.TaskDialogResult.Ok)
                {
                    //获得所有的选定的条码
                    foreach (KeyValuePair<string, Model.MANAGE_MAIN> stackCell in dicStack)
                    {
                        //获得起始位置
                        SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                        var value_ceLl_id = from v in listDataRowView
                                            where v["CELL_ID"].ToString() ==stackCell.Value.START_CELL_ID.ToString() && Convert.ToDecimal(v["MANAGE_LIST_QUANTITY"]) >0

                                            select v;

                        List<Model.MANAGE_LIST> listMANAGE_LIST = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.MANAGE_LIST>(value_ceLl_id.Cast<DataRowView>().ToArray(), null);

                        if (this.STORAGE_AREA_TYPE == Enum.AREA_TYPE.XuNiKu.ToString() && listMANAGE_LIST != null && listMANAGE_LIST.Count==0)
                        {
                            MainApp._MessageDialog.Show("请输入出库数量", null);
                            return;
                        }

                        mMANAGE_MAIN.MANAGE_TYPE_CODE = mMANAGE_TYPE.MANAGE_TYPE_CODE.TrimEnd();

                        mMANAGE_MAIN.PLAN_ID = intPlanID;

                        mMANAGE_MAIN.STOCK_BARCODE = stackCell.Value.STOCK_BARCODE;

                        mMANAGE_MAIN.CELL_MODEL = this.ucManagePosition.U_CELL_MODEL;

                        mMANAGE_MAIN.START_CELL_ID = stackCell.Value.START_CELL_ID;

                        mMANAGE_MAIN.END_CELL_ID = this.ucManagePosition.U_END_POSITION_ID;

                        mMANAGE_MAIN.MANAGE_OPERATOR = MainApp._USER.USER_NAME;

                        mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                        mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.Waitting.ToString();

                        mMANAGE_MAIN.MANAGE_LEVEL = string.Empty;

                        mMANAGE_MAIN.MANAGE_REMARK = string.Empty;


                        if (this.STORAGE_AREA_TYPE == Enum.AREA_TYPE.XuNiKu.ToString() && mMANAGE_MAIN.START_CELL_ID != mMANAGE_MAIN.END_CELL_ID)
                        {
                            MainApp._MessageDialog.Show("目标位置错误", null);
                            return;
                        }

                        //boolResult = MainApp.I_BaseService.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(), 
                        //                                             "ManageCreate", 
                        //                                              new object[] { 
                        //                                                             mMANAGE_MAIN, 
                        //                                                             listPLAN_LIST, 
                        //                                                             listMANAGE_LIST,
                        //                                                             true, 
                        //                                                             this.ucManagePosition.U_AutoDownloadControlTask, 
                        //                                                             this.ucManagePosition.U_AutoCompleteTask 
                        //                                                            }, 
                        //                                             out strResult);

                        boolResult = MainApp.I_ManageService.ManageCreate(
                           mMANAGE_MAIN,
                           listMANAGE_LIST,
                           raiseTrans: true,
                           checkStorage: true,
                           checkManage: true,
                           checkCellStatus: true,
                           autoComplete: this.ucManagePosition.U_AutoCompleteTask,
                           autoControl: this.ucManagePosition.U_AutoDownloadControlTask,
                           out strResult);

                        //判断结果
                        if (!boolResult)
                        {
                            break;
                        }
                        else
                        {
                            break;
                        }
                    }

                    MainApp._MessageDialog.ShowResult(boolResult, strResult);

                    this.ucManagePosition.U_Refresh();
                      
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        ///// <summary>
        ///// 获得托盘集合列表
        ///// </summary>
        private IDictionary<string, Model.MANAGE_MAIN> GetPalletKeyValuePair(DataRowView[] listDataRowView)
        {
            IDictionary<string, Model.MANAGE_MAIN> dicStack = new Dictionary<string, Model.MANAGE_MAIN>();
            foreach (DataRowView rowView in listDataRowView)
            {
                string stack =  rowView["CELL_ID"].ToString();
                if (stack != string.Empty)
                {
                    //获得货位编号
                    //SiaSun.LMS.Model.WH_CELL mWH_CELL = (Model.WH_CELL)MainApp.I_BaseService.GetModel("WH_CELL_SELECT_BY_ID", Convert.ToInt32(rowView["CELL_ID"])).RequestObject;
                    if (!dicStack.ContainsKey(stack))
                    {
                        Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                        mMANAGE_MAIN.STOCK_BARCODE = rowView["STOCK_BARCODE"].ToString();

                        mMANAGE_MAIN.START_CELL_ID = Convert.ToInt32(rowView["CELL_ID"]);

                        dicStack.Add(stack, mMANAGE_MAIN);
                    }
                }
            }
            return dicStack;
        }


        private void Query()
        {
            DataRowView[] checkDataRowView = this.gridPlanList.U_GetCheckedDataRows();

            if (checkDataRowView.Length == 0)
            {
                MainApp._MessageDialog.Show(Enum.MessageConverter.SelectCount);
                return;
            }

            

            this.ucManagePosition.U_Refresh();

            string sPLAN_LIST_ID = string.Empty;

            foreach (DataRowView drv in checkDataRowView)
            {
                sPLAN_LIST_ID += drv["PLAN_LIST_ID"].ToString() + ",";
            }

            sPLAN_LIST_ID = sPLAN_LIST_ID.TrimEnd(',');

            string GOODS_PROPERTY = string.Empty;

            IList<SiaSun.LMS.Model.GOODS_PROPERTY> lsGOODS_PROPERTY = MainApp.I_GoodsService.GoodsPropertyGetListGoodsTypeID(this.gridPlanList.U_GetSelectedGoodsTypeID());

            foreach (SiaSun.LMS.Model.GOODS_PROPERTY mGOODS_PROPERTY in lsGOODS_PROPERTY)
            {
                if( mGOODS_PROPERTY.GOODS_PROPERTY_KEYFLAG=="1")
                    GOODS_PROPERTY += string.Format(" and ISNULL({0}.{1},ISNULL({2}.{1},'1')) = ISNULL({2}.{1},'1')", "PLAN_LIST", mGOODS_PROPERTY.GOODS_PROPERTY_FIELD, "V_STORAGE_LIST");
            }
            
            string sPLAN = " (select count(0) FROM PLAN_LIST WHERE V_STORAGE_LIST.GOODS_ID= PLAN_LIST.GOODS_ID {2} {0} AND PLAN_LIST_ID in ({1}))>0";

            this.StorageListBind(string.Format(sPLAN, GOODS_PROPERTY, sPLAN_LIST_ID, this.STORAGE_AREA_TYPE == string.Empty ? string.Empty: string.Format(" AND V_STORAGE_LIST.AREA_TYPE = '{0}' ",this.STORAGE_AREA_TYPE) ));
        }
        


    }
}
