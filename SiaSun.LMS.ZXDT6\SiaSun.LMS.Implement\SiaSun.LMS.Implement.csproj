﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{10C5DA09-0D1E-4AE2-80C4-FB7B916FA850}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SiaSun.LMS.Implement</RootNamespace>
    <AssemblyName>SiaSun.LMS.Implement</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <AutoGenerateBindingRedirects>false</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BarcodeLib">
      <HintPath>..\SiaSun.LMS.Lib\BarcodeLib.dll</HintPath>
    </Reference>
    <Reference Include="IBatisNet.Common, Version=3.0.0.0, Culture=neutral, PublicKeyToken=ed781d9fc396c6ca, processorArchitecture=MSIL">
      <HintPath>..\packages\SqlBatis.Common.3.0.0\lib\netstandard2.0\IBatisNet.Common.dll</HintPath>
    </Reference>
    <Reference Include="IBatisNet.DataMapper">
      <HintPath>..\SiaSun.LMS.Persistence\bin\Debug\IBatisNet.DataMapper.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.17.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.17\lib\net45\log4net.dll</HintPath>
    </Reference>
    <Reference Include="RYB_PTL_API, Version=6.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\RYB_PTL_API.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Net" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.4.5.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Permissions.4.5.0\lib\net461\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.4.5.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Apply\Apply1.cs" />
    <Compile Include="Apply\ApplyBase.cs" />
    <Compile Include="Interface\InterfaceBase.cs" />
    <Compile Include="Interface\WMS\GoodsInfoSync.cs" />
    <Compile Include="Interface\iWMS\PalletTransportComplete.cs" />
    <Compile Include="Interface\WMS\PalletTransportRequest.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="S_Interface.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="S_Base.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="S_Manage.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="S_Plan.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="S_Database.cs" />
    <Compile Include="S_System.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="S_Device.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SiaSun.LMS.Common\SiaSun.LMS.Common.csproj">
      <Project>{FD10F3E5-A233-4F52-B4EE-33189D84DBEF}</Project>
      <Name>SiaSun.LMS.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiaSun.LMS.Model\SiaSun.LMS.Model.csproj">
      <Project>{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}</Project>
      <Name>SiaSun.LMS.Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiaSun.LMS.Interface\SiaSun.LMS.Interface.csproj">
      <Project>{612D8C56-ECEC-48B9-87F9-AF80BF81E070}</Project>
      <Name>SiaSun.LMS.Interface</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiaSun.LMS.Persistence\SiaSun.LMS.Persistence.csproj">
      <Project>{B820FA47-3514-49DB-8D33-96367A80234E}</Project>
      <Name>SiaSun.LMS.Persistence</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>