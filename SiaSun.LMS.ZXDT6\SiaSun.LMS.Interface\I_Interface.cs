﻿using System.ServiceModel;

namespace SiaSun.LMS.Interface
{
    [ServiceContract()]
    public interface I_Interface
    {
        [OperationContract]
        string CarrierOutRequest(string imputJson);

        [OperationContract]
        string ComPick(string inputJson);

        [OperationContract]
        string GetAsrsStorage(string imputJson);

        [OperationContract]
        string GoodsInfoSync(string imputJson);

        [OperationContract]
        string PalletTransportRequest(string imputJson);

        [OperationContract]
        string QueryTaskOutProgress(string imputJson);

        [OperationContract]
        string StockTypeUpdate(string inputJson);

        [OperationContract]
        string StorageTransform(string imputJson);

        [OperationContract]
        string TaskOutCompleteConfirm(string inputJson);

        [OperationContract]
        string TaskOutRequest(string imputJson);

        [OperationContract]
        string TaskOutConfirm(string imputJson);

        [OperationContract]
        string UpdateTaskOutStatus(string imputJson);

        [OperationContract]
        string StorageBinRead(string inputJson);

        [OperationContract]
        string TransferToBin(string inputJson);

        [OperationContract]

        string TaskComfirm(string inputJson);

        [OperationContract]
        string PickNoRelease(string inputJson);

        [OperationContract]
        string PlanePickLock(string inputJson);
    }
}
