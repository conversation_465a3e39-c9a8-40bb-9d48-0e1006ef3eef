using System.Web.Http;
using Owin;

namespace SiaSun.LMS.HttpApiDemo
{
    public class Startup
    {
        public void Configuration(IAppBuilder app)
        {
            HttpConfiguration config = new HttpConfiguration();
            
            // 配置路由
            config.Routes.MapHttpRoute(
                name: "Default<PERSON><PERSON>",
                routeTemplate: "api/{controller}/{action}",
                defaults: new { id = RouteParameter.Optional }
            );

            // 配置JSON序列化
            config.Formatters.JsonFormatter.SerializerSettings.Formatting = 
                Newtonsoft.Json.Formatting.Indented;

            app.UseWebApi(config);
        }
    }
}