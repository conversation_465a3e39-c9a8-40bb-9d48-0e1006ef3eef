using System;
using Microsoft.Owin.Hosting;

namespace SiaSun.LMS.HttpApiDemo
{
    class Program
    {
        static void Main(string[] args)
        {
            string baseUrl = "http://localhost:9001";
            
            try
            {
                // 启动HTTP API服务
                using (WebApp.Start<Startup>(baseUrl))
                {
                    Console.WriteLine($"HTTP API Demo服务已启动: {baseUrl}");
                    Console.WriteLine("\n可用接口:");
                    Console.WriteLine($"GET  {baseUrl}/api/demo/status");
                    Console.WriteLine($"POST {baseUrl}/api/demo/carrier");
                    Console.WriteLine($"POST {baseUrl}/api/demo/task");
                    Console.WriteLine("\n按任意键停止服务...");
                    
                    Console.ReadKey();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"启动失败: {ex.Message}");
                Console.ReadKey();
            }
        }
    }
}