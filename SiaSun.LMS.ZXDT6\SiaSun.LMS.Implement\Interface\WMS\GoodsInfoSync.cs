﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface.WMS
{
    /// <summary>
    /// 4.16 同步物料信息接口 【SSWMS提供，iWMS调用】
    /// </summary>
    public class GoodsInfoSync : InterfaceBase
    {
        class InputParam
        {
            public PublicHeader Header { get; set; }
            public InputDataField DataField { get; set; }
        }
        class InputDataField : PublicInputBody
        {
            public string GoodsCode { get; set; }
            public string GoodsName { get; set; }
            public string GoodsType { get; set; }
            public string BreakCartonArea { get; set; }
            public string ContainsLable { get; set; }
            public string GoodsRemark { get; set; }
        }
        class OutputParam
        {
            public PublicHeader Header { get; set; }
            public PublicOutputBody DataField { get; set; }
        }

        internal override string IntefaceMethod(string inputJson)
        {
            bool result = true;
            string message = string.Empty;
            string outputJson = string.Empty;
            OutputParam outputParam = null;

            try
            {
                InputParam inputParam = Common.JsonHelper.Deserialize<InputParam>(inputJson);
                if (inputParam == null)
                {
                    result = false;
                    message = $"调用入参[{inputJson}]解析错误";
                    return FormatErrorMessage(message);
                }
                if (inputParam.Header.AppToken != interfaceToken)
                {
                    result = false;
                    message = $"调用提供的Token未被允许";
                    return FormatErrorMessage(message);
                }

                if(string.IsNullOrEmpty(inputParam.DataField.GoodsCode) || string.IsNullOrEmpty(inputParam.DataField.GoodsType)||
                    string.IsNullOrEmpty(inputParam.DataField.GoodsName) || string.IsNullOrEmpty(inputParam.DataField.ContainsLable))
                {
                    result = false;
                    message = $"接口入参必填项存在空值";
                    return FormatErrorMessage(message);
                }

                //if(!(inputParam.DataField.BreakCartonArea ==Enum.AreaCode.Carton.ToString()
                //    || inputParam.DataField.BreakCartonArea == Enum.AreaCode.Bulk.ToString()))
                //{
                //    result = false;
                //    message = $"接口入参拆箱区域字段{inputParam.DataField.BreakCartonArea}有误";
                //    return FormatErrorMessage(message);
                //}

                if (!ZeroOne.Contains(inputParam.DataField.ContainsLable))
                {
                    result = false;
                    message = $"接口入参拆箱区域字段{inputParam.DataField.ContainsLable}有误";
                    return FormatErrorMessage(message);
                }

                Model.GOODS_CLASS goodsClass = S_Base.sBase.pGOODS_CLASS.GetModel(inputParam.DataField.GoodsType);
                //Model.GOODS_CLASS goodsClass = S_Base.sBase.pGOODS_CLASS.GetModel("001");
                if (goodsClass == null)
                {
                    result = false;
                    message = $"物料类别{inputParam.DataField.GoodsType}未定义";
                    return FormatErrorMessage(message);
                }

                Model.GOODS_MAIN goodsMain = S_Base.sBase.pGOODS_MAIN.GetModel(inputParam.DataField.GoodsCode);
                if (goodsMain == null)
                {
                    goodsMain = new Model.GOODS_MAIN()
                    {
                        GOODS_CLASS_ID = goodsClass.GOODS_CLASS_ID,
                        GOODS_CODE = inputParam.DataField.GoodsCode,
                        //GOODS_CONST_PROPERTY1 = inputParam.DataField.BreakCartonArea,
                        GOODS_CONST_PROPERTY2 = inputParam.DataField.ContainsLable,
                        GOODS_CONST_PROPERTY3 = Common.StringUtil.GetDateTime(),
                        GOODS_FLAG = "1",
                        GOODS_NAME = inputParam.DataField.GoodsName,
                        GOODS_REMARK = inputParam.DataField.GoodsRemark,
                    };

                    S_Base.sBase.pGOODS_MAIN.Add(goodsMain);
                }
                else
                {
                    //goodsMain.GOODS_CONST_PROPERTY1 = inputParam.DataField.BreakCartonArea;
                    goodsMain.GOODS_CONST_PROPERTY2 = inputParam.DataField.ContainsLable;
                    goodsMain.GOODS_CONST_PROPERTY3 = Common.StringUtil.GetDateTime();
                    goodsMain.GOODS_FLAG = "1";
                    goodsMain.GOODS_NAME = inputParam.DataField.GoodsName;
                    goodsMain.GOODS_REMARK = inputParam.DataField.GoodsRemark;

                    S_Base.sBase.pGOODS_MAIN.Update(goodsMain);
                }
            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("异常_信息[{0}]", ex.Message);
            }
            finally
            {
                outputParam = new OutputParam()
                {
                    Header = new PublicHeader(),
                    DataField = new PublicOutputBody()
                    {
                        ResultFlag = result ? "1" : "0",
                        ErrorMessage = result ? "" : message
                    }
                };
                outputJson = Common.JsonHelper.Serializer(outputParam);
            }

            return outputJson;
        }
    }
}
