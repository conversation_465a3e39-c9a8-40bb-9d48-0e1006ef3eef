﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.MANAGE
{
    /// <summary>
    /// MANAGE_IN.xaml 的交互逻辑
    /// </summary>
    public partial class MANAGE_PLAN_IN : AvalonDock.DocumentContent
    {
        int intPlanID = 0;

        Model.PLAN_MAIN mPLAN_MAIN = null;

        Model.MANAGE_TYPE mMANAGE_TYPE = null;

        Model.MANAGE_TYPE_PARAM mMANAGE_TYPE_PARAM = null;


        #region    ------构造函数

        //构造函数
        public MANAGE_PLAN_IN(int PLAN_ID, string MANAGE_TYPE_CODE)
        {
            InitializeComponent();
            
            this.intPlanID = PLAN_ID;

            this.mMANAGE_TYPE =(Model.MANAGE_TYPE) MainApp.I_BaseService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE).RequestObject;
            
        }

        #endregion 

        /// <summary>
        /// 窗体加载
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (intPlanID > 0)
                {
                    mPLAN_MAIN = MainApp.I_PlanService.PlanGetModel(intPlanID);

                    if (mPLAN_MAIN == null)
                    {
                        MainApp._MessageDialog.Show("Plan.CheckPlanExists",new object[]{ intPlanID});
                    }              
                }

                //初始化任务类型控件
                this.InitManagePosition();

                this.PLAN_LIST_Bind(intPlanID);

            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        private void InitManagePosition()
        {
            try
            {
                this.mMANAGE_TYPE_PARAM = this.ucManagePosition.U_InitControl(mMANAGE_TYPE.MANAGE_TYPE_ID);
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }


        private void Register_DataTable_Event()
        {
            //当输入列值改变后验证数据是否合法
            foreach (TabItem tabItem in this.gridPlanList.tabSplitProperty.Items)
            {
                if (tabItem.HasContent)
                {
                    DataTable tableSource = (tabItem.Content as UC.ucCommonDataGrid).U_DataSource.Table;

                    tableSource.ColumnChanged -= new DataColumnChangeEventHandler(table_ColumnChanged);
                    tableSource.ColumnChanged += new DataColumnChangeEventHandler(table_ColumnChanged);
                }
            }
        }


        /// <summary>
        /// 表单数据校验
        /// </summary>
        private void table_ColumnChanged(object sender, DataColumnChangeEventArgs e)
        {
            bool bResult = true;
            string sResult = string.Empty;
            //判断列
            switch (e.Column.ColumnName)
            {
                case "MANAGE_LIST_QUANTITY":
                    bResult = (Convert.ToDecimal(e.ProposedValue) <= Convert.ToDecimal(e.Row["PLAN_LIST_QUANTITY"]) - Convert.ToDecimal(e.Row["PLAN_LIST_ORDERED_QUANTITY"]));

                    sResult = string.Format("入库数量不能大于计划数量!");
                    e.Row.ClearErrors();
                    if (bResult)
                    {
                        e.Row.SetColumnError(e.Column, null);
                    }
                    else
                    {
                        e.Row.SetColumnError(e.Column, sResult);
                    }
                    break;
            }


        }


        /// <summary>
        /// 加载
        /// </summary>
        private void PLAN_LIST_Bind(int PLAN_ID)
        {
            //数据源属性
            this.gridPlanList.U_Clear();
            this.gridPlanList.U_WindowName = this.GetType().Name;
            this.gridPlanList.U_TableName = "V_PLAN_LIST";
            this.gridPlanList.U_XmlTableName = "V_PLAN_LIST";
            this.gridPlanList.U_Fields = "*";
            this.gridPlanList.U_Where = string.Format("PLAN_STATUS ='{0}' AND PLAN_TYPE_CODE LIKE '%In%'", Enum.PLAN_STATUS.Executing.ToString());
            this.gridPlanList.U_OrderField = "PLAN_LIST_ID";

            this.gridPlanList.U_AllowChecked = false;
            this.gridPlanList.U_AllowOperatData = false;
            this.gridPlanList.U_AllowShowPage = false;

            //拆分列属性
            this.gridPlanList.U_SplitPropertyType = "GOODS_TYPE";
            this.gridPlanList.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.gridPlanList.U_SplitGroupHeader = "GOODS_TYPE.GOODS_TYPE_NAME";
            this.gridPlanList.U_SplitPropertyColumn = "GOODS_PROPERTY";

            //明细属性
            //this.gridPlanList.U_DetailTableName = "PLAN_DETAIL";
            //this.gridPlanList.U_DetailRelatvieColumn = "PLAN_LIST_ID";
            try
            {
                //初始化控件
                this.gridPlanList.U_InitControl();
                Register_DataTable_Event();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }


        #region     ------按钮事件

        /// <summary>
        /// 按钮事件
        /// </summary>
        private void Button_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnSave":
                        this.Confirm();
                        break;
                    case "btnRefresh":
                        this.Refresh();
                        break;
                }
            }
        }

        /// <summary>
        /// 确认
        /// </summary>
        private void Confirm()
        {
            string strResult = string.Empty;

            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;

                //结束当前编辑操作
                this.gridPlanList.U_EndCurrentEdit();
                
                //检验数据是否合法
                DataTable tableSource = this.gridPlanList.U_DataSource;
                //根据数据源获得数据列表
                List<Model.MANAGE_LIST> listMANAGE_LIST = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.MANAGE_LIST>(tableSource, null);



                var list = from v in listMANAGE_LIST
                           where v.MANAGE_LIST_QUANTITY > 0
                           select v;

                listMANAGE_LIST = list.ToList<Model.MANAGE_LIST>();

                #region     ------校验合法性

                //判断是否填写数据
                if (listMANAGE_LIST.Count<=0)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.Input);
                    return;
                }

                //判断数据是否合法
                if (tableSource.HasErrors)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.Data);
                    return;
                }


                //校验填写仓库信息是否合法
                if (!this.ucManagePosition.U_CHECK_WAREHOUSE())
                    return;

                #endregion



                //提示确认
                if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.ConfirmAssembly, this.ucManagePosition.U_STOCK_BARCODE)== Sid.Windows.Controls.TaskDialogResult.Ok)
                {
                    //调用入库函数配盘入库

                    SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                    mMANAGE_MAIN.PLAN_ID = this.intPlanID;

                    mMANAGE_MAIN.MANAGE_TYPE_CODE = mMANAGE_TYPE.MANAGE_TYPE_CODE.TrimEnd();

                    mMANAGE_MAIN.STOCK_BARCODE = this.ucManagePosition.U_STOCK_BARCODE;

                    mMANAGE_MAIN.CELL_MODEL = this.ucManagePosition.U_CELL_MODEL;

                    mMANAGE_MAIN.FULL_FLAG = this.ucManagePosition.U_OCCUPY_PERCENT;

                    mMANAGE_MAIN.START_CELL_ID = this.ucManagePosition.U_START_POSITION_ID;

                    mMANAGE_MAIN.END_CELL_ID = this.ucManagePosition.U_END_POSITION_ID;

                    mMANAGE_MAIN.MANAGE_OPERATOR = MainApp._USER.USER_NAME;

                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                    mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.Waitting.ToString();

                    mMANAGE_MAIN.MANAGE_LEVEL = string.Empty;

                    mMANAGE_MAIN.MANAGE_REMARK = string.Empty;

                    bool bResult = false;
                    //创建托盘任务

                    bResult = MainApp.I_BaseService.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(),
                                                               "ManageCreate", 
                                                                new object[] { mMANAGE_MAIN,
                                                                               listMANAGE_LIST, 
                                                                               true, 
                                                                               mMANAGE_TYPE_PARAM
                                                                               }, 
                                                                 out strResult);


                    //检验执行结果
                    if (bResult)
                    {
                        if( !this.rbRefresh.IsChecked.Value)
                            this.gridPlanList.U_Update();

                        this.Refresh();
                    }

                    MainApp._MessageDialog.ShowResult(bResult, strResult);

                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            finally
            {
                MainWindow.mainWin.Cursor = Cursors.Arrow;
            }
        }

        /// <summary>
        /// 刷新
        /// </summary>
        private void Refresh()
        {
            this.ucManagePosition.U_Refresh();
        }

        #endregion
    }
}
