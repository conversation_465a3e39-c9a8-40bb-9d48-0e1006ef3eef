﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{FC347234-7FF2-4758-9C63-BC8E1553C844}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SiaSun.LMS.WPFClient</RootNamespace>
    <AssemblyName>SiaSun.LMS.WPFClient</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <IsWebBootstrapper>true</IsWebBootstrapper>
    <PublishUrl>D:\WMS_CLIENT_PUB\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Web</InstallFrom>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>true</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <InstallUrl>http://192.168.18.1/</InstallUrl>
    <TargetCulture>zh-CN</TargetCulture>
    <ProductName>新松智能物流管理系统客户端</ProductName>
    <PublisherName>新松智能物流管理系统客户端</PublisherName>
    <SuiteName>新松智能物流管理系统客户端</SuiteName>
    <MinimumRequiredVersion>3.0.2.25</MinimumRequiredVersion>
    <CreateWebPageOnPublish>true</CreateWebPageOnPublish>
    <WebPage>index.htm</WebPage>
    <ApplicationRevision>70</ApplicationRevision>
    <ApplicationVersion>3.0.2.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <CreateDesktopShortcut>true</CreateDesktopShortcut>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <BootstrapperComponentsLocation>Relative</BootstrapperComponentsLocation>
    <BootstrapperComponentsUrl>D:\WMS_CLIENT_PUB\ClickOnce文件</BootstrapperComponentsUrl>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject>SiaSun.LMS.WPFClient.MainApp</StartupObject>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>%40Images\SiasunWMS.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>5D60731600BE649BA27A9B7A5AEEC84B3576E612</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>SiaSun.LMS.WPFClient_1_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>false</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>false</SignManifests>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>false</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <TargetZone>LocalIntranet</TargetZone>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>Properties\app.manifest</ApplicationManifest>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AvalonDock, Version=1.3.3600.0, Culture=neutral, PublicKeyToken=85a1e0ada7ec13e4, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\zjfn_asrs\SSWMS_ZJFN\SiaSun.LMS.WPFClient\bin\Client\AvalonDock.dll</HintPath>
    </Reference>
    <Reference Include="AvalonDock.Themes">
      <HintPath>bin\Client\AvalonDock.Themes.dll</HintPath>
    </Reference>
    <Reference Include="BarcodeLib">
      <HintPath>..\..\Lib\BarcodeLib.dll</HintPath>
    </Reference>
    <Reference Include="CodeReason.Reports, Version=0.5.0.0, Culture=neutral, PublicKeyToken=74fc60a060db7911, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>@Dll\CodeReason.Reports.dll</HintPath>
    </Reference>
    <Reference Include="ExampleClassLibrary, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>@Dll\ExampleClassLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Fluent, Version=2.1.0.0, Culture=neutral, PublicKeyToken=3e436e32a8c5546f, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>@Dll\Fluent.dll</HintPath>
    </Reference>
    <Reference Include="Gma.QrCodeNet.Encoding">
      <HintPath>@Dll\Gma.QrCodeNet.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WinForms, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>@Dll\Microsoft.ReportViewer.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Windows.Shell">
      <HintPath>bin\Client\Microsoft.Windows.Shell.dll</HintPath>
    </Reference>
    <Reference Include="PresentationFramework.Luna" />
    <Reference Include="ReachFramework" />
    <Reference Include="RibbonControlsLibrary, Version=4.0.0.11019, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\RibbonControlsLibrary.4.0.0\lib\net40\RibbonControlsLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Sid.TaskDialog, Version=3.5.0.0, Culture=neutral, PublicKeyToken=848068847aa7ec0e, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>@Dll\Sid.TaskDialog.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Printing" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="UIAutomationProvider" />
    <Reference Include="UIAutomationTypes" />
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="WindowsFormsIntegration" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="MainApp.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="DIALOG\BarCodePlanarDialog.xaml.cs">
      <DependentUpon>BarCodePlanarDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="DIALOG\DataGridDialog.xaml.cs">
      <DependentUpon>DataGridDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="DIALOG\DataGridWindow.xaml.cs">
      <DependentUpon>DataGridWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="DIALOG\DateSectQueryWindow.xaml.cs">
      <DependentUpon>DateSectQueryWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="DIALOG\DateSectSplitQueryWindow.xaml.cs">
      <DependentUpon>DateSectSplitQueryWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="DIALOG\EditPanelDialog.xaml.cs">
      <DependentUpon>EditPanelDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="DIALOG\GoodsPositionTemplate.xaml.cs">
      <DependentUpon>GoodsPositionTemplate.xaml</DependentUpon>
    </Compile>
    <Compile Include="DIALOG\ImportExcelDialog.xaml.cs">
      <DependentUpon>ImportExcelDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="DIALOG\Password.xaml.cs">
      <DependentUpon>Password.xaml</DependentUpon>
    </Compile>
    <Compile Include="DIALOG\ImportGoodsDialog.xaml.cs">
      <DependentUpon>ImportGoodsDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="FLOW\APPLY_ACTION.xaml.cs">
      <DependentUpon>APPLY_ACTION.xaml</DependentUpon>
    </Compile>
    <Compile Include="GOODS\GOODS_CLASS_EDIT.xaml.cs">
      <DependentUpon>GOODS_CLASS_EDIT.xaml</DependentUpon>
    </Compile>
    <Compile Include="GOODS\GOODS_IMPORT.xaml.cs">
      <DependentUpon>GOODS_IMPORT.xaml</DependentUpon>
    </Compile>
    <Compile Include="MANAGE\MANAGE_ADJUST.xaml.cs">
      <DependentUpon>MANAGE_ADJUST.xaml</DependentUpon>
    </Compile>
    <Compile Include="MANAGE\MANAGE_DOWN.xaml.cs">
      <DependentUpon>MANAGE_DOWN.xaml</DependentUpon>
    </Compile>
    <Compile Include="MANAGE\MANAGE_MOVE.xaml.cs">
      <DependentUpon>MANAGE_MOVE.xaml</DependentUpon>
    </Compile>
    <Compile Include="MANAGE\MANAGE_UP.xaml.cs">
      <DependentUpon>MANAGE_UP.xaml</DependentUpon>
    </Compile>
    <Compile Include="MANAGE\MANAGE_OUT.xaml.cs">
      <DependentUpon>MANAGE_OUT.xaml</DependentUpon>
    </Compile>
    <Compile Include="REPORT\Report.cs">
      <DependentUpon>Report.xsd</DependentUpon>
    </Compile>
    <Compile Include="REPORT\Report.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Report.xsd</DependentUpon>
    </Compile>
    <Compile Include="REPORT\StaticsReportWPF.xaml.cs">
      <DependentUpon>StaticsReportWPF.xaml</DependentUpon>
    </Compile>
    <Compile Include="SYS\APPLY_LOG_QUERY.xaml.cs">
      <DependentUpon>APPLY_LOG_QUERY.xaml</DependentUpon>
    </Compile>
    <Compile Include="SYS\CURRENT_APPLY.xaml.cs">
      <DependentUpon>CURRENT_APPLY.xaml</DependentUpon>
    </Compile>
    <Compile Include="SYS\SYS_USER.xaml.cs">
      <DependentUpon>SYS_USER.xaml</DependentUpon>
    </Compile>
    <Compile Include="SYS\TABLE_CONVERTER_EDIT.xaml.cs">
      <DependentUpon>TABLE_CONVERTER_EDIT.xaml</DependentUpon>
    </Compile>
    <Compile Include="DIALOG\MDI_DataGrid.xaml.cs">
      <DependentUpon>MDI_DataGrid.xaml</DependentUpon>
    </Compile>
    <Compile Include="DIALOG\MDI_TreeView_DataGrid.xaml.cs">
      <DependentUpon>MDI_TreeView_DataGrid.xaml</DependentUpon>
    </Compile>
    <Compile Include="DIALOG\MessageDialog.cs" />
    <Compile Include="DIALOG\QueryDialog.xaml.cs">
      <DependentUpon>QueryDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="DIALOG\RelationEdit.xaml.cs">
      <DependentUpon>RelationEdit.xaml</DependentUpon>
    </Compile>
    <Compile Include="DIALOG\ReportWindow.xaml.cs">
      <DependentUpon>ReportWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="GOODS\GOODS_EDIT.xaml.cs">
      <DependentUpon>GOODS_EDIT.xaml</DependentUpon>
    </Compile>
    <Compile Include="MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="MANAGE\MANAGE_IN.xaml.cs">
      <DependentUpon>MANAGE_IN.xaml</DependentUpon>
    </Compile>
    <Compile Include="PLAN\PLAN_EDIT.xaml.cs">
      <DependentUpon>PLAN_EDIT.xaml</DependentUpon>
    </Compile>
    <Compile Include="SYS\LOG_QUERY.xaml.cs">
      <DependentUpon>LOG_QUERY.xaml</DependentUpon>
    </Compile>
    <Compile Include="SYS\ROLE_WINDOW_CONTROL.xaml.cs">
      <DependentUpon>ROLE_WINDOW_CONTROL.xaml</DependentUpon>
    </Compile>
    <Compile Include="SYS\TECHINCS_ROUTE_CONFIG.xaml.cs">
      <DependentUpon>TECHINCS_ROUTE_CONFIG.xaml</DependentUpon>
    </Compile>
    <Compile Include="SYS\WORKFLOW_STATUS_CONFIG.xaml.cs">
      <DependentUpon>WORKFLOW_STATUS_CONFIG.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ComboBoxQuery\ComboBoxQuery.xaml.cs">
      <DependentUpon>ComboBoxQuery.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ComboBoxQuery\InfoWindow.xaml.cs">
      <DependentUpon>InfoWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\DataGridTemplateNobug.cs" />
    <Compile Include="UC\DrawingCanvas.cs" />
    <Compile Include="UC\ucBarCodePlanarControl.xaml.cs">
      <DependentUpon>ucBarCodePlanarControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucCommonDataGridNobug.xaml.cs">
      <DependentUpon>ucCommonDataGridNobug.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucCommonDataGrid.xaml.cs">
      <DependentUpon>ucCommonDataGrid.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucManageInInput.xaml.cs">
      <DependentUpon>ucManageInInput.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucManagePosition.xaml.cs">
      <DependentUpon>ucManagePosition.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucModelValuePanel.xaml.cs">
      <DependentUpon>ucModelValuePanel.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucNotMessagePad.xaml.cs">
      <DependentUpon>ucNotMessagePad.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucRelationEdit.xaml.cs">
      <DependentUpon>ucRelationEdit.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucSplitPropertyGridTab.xaml.cs">
      <DependentUpon>ucSplitPropertyGridTab.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucSplitPropertyPanel.xaml.cs">
      <DependentUpon>ucSplitPropertyPanel.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucDataGridRowGroupControl.xaml.cs">
      <DependentUpon>ucDataGridRowGroupControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucStoreCell.xaml.cs">
      <DependentUpon>ucStoreCell.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucWareHouseQuery.xaml.cs">
      <DependentUpon>ucWareHouseQuery.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucWindowTopHeader.xaml.cs">
      <DependentUpon>ucWindowTopHeader.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\Validate\ValidateLengthRule.cs" />
    <Compile Include="UC\Validate\ValidateNullEmptyRule.cs" />
    <Compile Include="UC\Validate\ValidateNumberRangeRule .cs" />
    <Compile Include="UC\ucStockIn.xaml.cs">
      <DependentUpon>ucStockIn.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucSotckOut.xaml.cs">
      <DependentUpon>ucSotckOut.xaml</DependentUpon>
    </Compile>
    <Compile Include="WH\CELL_THRESHOLD.xaml.cs">
      <DependentUpon>CELL_THRESHOLD.xaml</DependentUpon>
    </Compile>
    <Compile Include="WH\SET_CELL_GOODS.xaml.cs">
      <DependentUpon>SET_CELL_GOODS.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="WH\WAREHOUSE_AREA_MANAGE.xaml.cs">
      <DependentUpon>WAREHOUSE_AREA_MANAGE.xaml</DependentUpon>
    </Compile>
    <Compile Include="WH\WAREHOUSE_CELL_SELECT.xaml.cs">
      <DependentUpon>WAREHOUSE_CELL_SELECT.xaml</DependentUpon>
    </Compile>
    <Compile Include="WH\WAREHOUSE_CELL_STATUS.xaml.cs">
      <DependentUpon>WAREHOUSE_CELL_STATUS.xaml</DependentUpon>
    </Compile>
    <Compile Include="FLOW\CONTROL_ACTION.xaml.cs">
      <DependentUpon>CONTROL_ACTION.xaml</DependentUpon>
    </Compile>
    <Compile Include="FLOW\MANAGE_ACTION.xaml.cs">
      <DependentUpon>MANAGE_ACTION.xaml</DependentUpon>
    </Compile>
    <Compile Include="FLOW\PLAN_ACTION.xaml.cs">
      <DependentUpon>PLAN_ACTION.xaml</DependentUpon>
    </Compile>
    <Compile Include="SelectRole.xaml.cs">
      <DependentUpon>SelectRole.xaml</DependentUpon>
    </Compile>
    <Compile Include="SERVICE\CustomerDescriptions.cs" />
    <Compile Include="SYS\APP_CONFIG.xaml.cs">
      <DependentUpon>APP_CONFIG.xaml</DependentUpon>
    </Compile>
    <Compile Include="SYS\FIELD_DESCRIPTION.xaml.cs">
      <DependentUpon>FIELD_DESCRIPTION.xaml</DependentUpon>
    </Compile>
    <Compile Include="SYS\MENU_EDIT.xaml.cs">
      <DependentUpon>MENU_EDIT.xaml</DependentUpon>
    </Compile>
    <Compile Include="SYS\WINDOW_STYLE.xaml.cs">
      <DependentUpon>WINDOW_STYLE.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\DataGridTemplate.cs" />
    <Compile Include="UC\ucEditPanel.xaml.cs">
      <DependentUpon>ucEditPanel.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucMdiDataGridControl.xaml.cs">
      <DependentUpon>ucMdiDataGridControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucMdiTreeViewDataGridControl.xaml.cs">
      <DependentUpon>ucMdiTreeViewDataGridControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucQuery.xaml.cs">
      <DependentUpon>ucQuery.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucQuickQuery.xaml.cs">
      <DependentUpon>ucQuickQuery.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucStatusFlowActionsPanel.xaml.cs">
      <DependentUpon>ucStatusFlowActionsPanel.xaml</DependentUpon>
    </Compile>
    <Compile Include="UC\ucTreeView.xaml.cs">
      <DependentUpon>ucTreeView.xaml</DependentUpon>
    </Compile>
    <Content Include="%40Dll\BarcodeLib.dll" />
    <Content Include="%40Dll\CodeReason.Reports.dll" />
    <Content Include="%40Dll\DSBarCode.dll" />
    <Content Include="%40Dll\ExampleClassLibrary.dll" />
    <Content Include="%40Dll\Fluent.dll" />
    <Content Include="%40Dll\Interop.TAPI3Lib.dll" />
    <Resource Include="%40Images\BlueLarge.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\二维码.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\仓库设置.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\任务明细查询.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\任务管理.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\任务统计.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\任务记录查询.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\修改密码.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\初始化.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\叠盘.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\周转箱任务.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\导入计划.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\巷道维护.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\帮助.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\库存查询.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\库存统计.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\应用.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\录入计划.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\待办任务.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\托盘物料入库.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\托盘物料出库.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\扫描条码.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\指令管理.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\日志.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\权限维护.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\样式设置.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\流程设置.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\物料.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\生产线维护.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\用户管理.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\登陆.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\码垛机.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\移库任务.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\空周转箱入库.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\空周转箱出库.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\空托盘入库.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\空托盘出库.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\站台维护.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\系统.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\组盘.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\组箱.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\编码.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\菜单.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\菜单设置.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\计划单查询.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\计算器.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\记事本.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\货位状态.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\货位维护.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\货位设置.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\退出系统.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\选择角色.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\配置.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\重新加载.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\角色定义.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\待办工作.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\上架.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\下架.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\接口.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\系统设置.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\系统界面.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\系统功能.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\closeAll.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\仓库.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\任务处理.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\出入库查询.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\功能.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\实托盘出库.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\导入.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\库存调整.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\托盘合并.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\日志查询.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\样式.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\流程.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\用户权限.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\系统编码.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\编码维护.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\补盘.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\角色.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\a2.jpg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\待办计划.ico" />
    <Resource Include="%40Images\货位显示.png" />
    <Resource Include="%40Images\控制任务.png" />
    <Resource Include="%40Images\路径维护.ico" />
    <Resource Include="%40Images\异常.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Page Include="%40Styles\Button.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\ButtonTemplate.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\CheckBox.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\ComboBox.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\DataGrid.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\GridSplitter.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\GroupBox.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\Label.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\ListBox.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\ListView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\Menu.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\ProgressBar.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\RadionButton.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\ScrollBar.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\Separator.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\Shared.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\Slider.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\StatusBar.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\TabControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\TextBlock.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\TextBox.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\ToggleButton.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\ToolBar.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\ToolTip.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\TreeView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\Window.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="DIALOG\BarCodePlanarDialog.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="DIALOG\DataGridDialog.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="DIALOG\DataGridWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="DIALOG\DateSectQueryWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="DIALOG\DateSectSplitQueryWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="DIALOG\EditPanelDialog.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="DIALOG\GoodsPositionTemplate.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="DIALOG\ImportExcelDialog.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="DIALOG\Password.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="DIALOG\ImportGoodsDialog.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="FLOW\APPLY_ACTION.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="GOODS\GOODS_CLASS_EDIT.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="GOODS\GOODS_IMPORT.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="MANAGE\MANAGE_ADJUST.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="MANAGE\MANAGE_DOWN.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="MANAGE\MANAGE_MOVE.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="MANAGE\MANAGE_UP.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="MANAGE\MANAGE_OUT.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="REPORT\StaticsReportWPF.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="SYS\APPLY_LOG_QUERY.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SYS\CURRENT_APPLY.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SYS\SYS_USER.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="SYS\TABLE_CONVERTER_EDIT.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="DIALOG\MDI_DataGrid.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="DIALOG\MDI_TreeView_DataGrid.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="DIALOG\QueryDialog.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="DIALOG\RelationEdit.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="DIALOG\ReportWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="GOODS\GOODS_EDIT.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Login.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Compile Include="MainApp.xaml.cs">
      <DependentUpon>MainApp.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Login.xaml.cs">
      <DependentUpon>Login.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Page Include="MainWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="MANAGE\MANAGE_IN.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="PLAN\PLAN_EDIT.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SYS\LOG_QUERY.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SYS\ROLE_WINDOW_CONTROL.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SYS\TECHINCS_ROUTE_CONFIG.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SYS\WORKFLOW_STATUS_CONFIG.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ComboBoxQuery\ComboBoxQuery.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UC\ComboBoxQuery\InfoWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UC\ucBarCodePlanarControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucCommonDataGridNobug.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UC\ucManageInInput.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucManagePosition.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucModelValuePanel.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucNotMessagePad.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucRelationEdit.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucSplitPropertyGridTab.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucSplitPropertyPanel.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucDataGridRowGroupControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucStoreCell.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucWareHouseQuery.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucWindowTopHeader.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucStockIn.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UC\ucSotckOut.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="WH\CELL_THRESHOLD.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="WH\SET_CELL_GOODS.xaml">
      <SubType>Designer</SubType>
      <Generator>XamlIntelliSenseFileGenerator</Generator>
    </Page>
    <Page Include="WH\WAREHOUSE_AREA_MANAGE.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WH\WAREHOUSE_CELL_SELECT.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WH\WAREHOUSE_CELL_STATUS.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="FLOW\CONTROL_ACTION.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="FLOW\MANAGE_ACTION.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="FLOW\PLAN_ACTION.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SelectRole.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="%40Styles\ApplicationStyles.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\BrushStyles.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\ButtonStyles.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="%40Styles\DataGridStyles.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Page>
    <Page Include="SYS\APP_CONFIG.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SYS\FIELD_DESCRIPTION.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SYS\MENU_EDIT.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SYS\WINDOW_STYLE.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucCommonDataGrid.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucEditPanel.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucMdiDataGridControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucMdiTreeViewDataGridControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucQuery.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucQuickQuery.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucStatusFlowActionsPanel.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UC\ucTreeView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Content Include="%40Dll\Gma.QrCodeNet.Encoding.dll" />
    <Content Include="%40Dll\Sid.TaskDialog.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="%40Files\en-US\FieldDescription.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="%40Files\en-US\FormStyles.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <EmbeddedResource Include="REPORT\InOutReport.rdlc" />
    <EmbeddedResource Include="REPORT\Report1.rdlc" />
    <EmbeddedResource Include="REPORT\Report_GOODS_INOUT.rdlc" />
    <Resource Include="%40Images\trimsize.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\source.psd">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="packages.config" />
    <None Include="Properties\app.manifest" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <AppDesigner Include="Properties\" />
    <None Include="REPORT\Report.xsc">
      <DependentUpon>Report.xsd</DependentUpon>
    </None>
    <None Include="REPORT\Report.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>Report.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <None Include="REPORT\Report.xss">
      <DependentUpon>Report.xsd</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="%40Images\feed_add.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\logo.bmp">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="%40Dll\PresentationCore.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="%40Dll\PresentationFramework.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="%40Dll\WindowsBase.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="%40Files\Default\FieldDescription.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </Content>
    <Content Include="%40Files\Default\FormStyles.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </Content>
    <Resource Include="%40Images\add.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\back.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\cancel.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\close.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\close_folder.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\copy.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\cut.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\delete.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\edit.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\exit.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\export_excel.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\find.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\first.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\font.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\help.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\import_excel.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\keyboard.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\last.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\left.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\list.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\monitor.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\new.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\next.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\ok.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\open_folder.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\order.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\paste.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\pause.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\printer.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\print_preview.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\reduce.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\refresh.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\report.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\report_go.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\right.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\role.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\save.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\search.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\SiasunWMS.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\sql.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\start.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\stop.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\toolbox.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\zoom_in.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="%40Images\zoom_out.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="stdole">
      <Guid>{00020430-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>2</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.4.5">
      <Visible>False</Visible>
      <ProductName>Windows Installer 4.5</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <PublishFile Include="%40Files\Default\FieldDescription.xml">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="%40Files\Default\FormStyles.xml">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="%40Files\en-US\FieldDescription.xml">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="%40Files\en-US\FormStyles.xml">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="%40Images\%40Files\Default\FieldDescription.xml">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="%40Images\%40Files\Default\FormStyles.xml">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="%40Images\%40Files\en-US\FieldDescription.xml">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="%40Images\%40Files\en-US\FormStyles.xml">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="%40Templates\ImportExcel.xml">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.ReportViewer.Common">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.ReportViewer.DataVisualization">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.ReportViewer.ProcessingObjectModel">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.ReportViewer.WinForms">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.SqlServer.Types">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Sid.TaskDialog">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="UC\Pro\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SiaSun.LMS.Common\SiaSun.LMS.Common.csproj">
      <Project>{FD10F3E5-A233-4F52-B4EE-33189D84DBEF}</Project>
      <Name>SiaSun.LMS.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiaSun.LMS.Model\SiaSun.LMS.Model.csproj">
      <Project>{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}</Project>
      <Name>SiaSun.LMS.Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiaSun.LMS.Interface\SiaSun.LMS.Interface.csproj">
      <Project>{612D8C56-ECEC-48B9-87F9-AF80BF81E070}</Project>
      <Name>SiaSun.LMS.Interface</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>