<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<configSections>
	</configSections>
	<appSettings>

		<!--新松内部服务发布地址-->
		<add key="SiasunUrl" value="http://127.0.0.1:8002/Service"/>

		<!--新松对外接口服务发布地址-->
		<add key="InterfaceUrl" value="http://127.0.0.1:8001/Service"/>

		<!--外部系统接口服务地址-->
		<add key="ExternalServiceUrl" value="http://127.0.0.1:8080/Service1.asmx"/>

		<!--外部系统接口服务Token-->
		<add key="ExternalServiceToken" value="456"/>
		<!--新松接口服务Token-->
		<add key="InterfaceToken" value="456"/>

		<!--数据库类型 Oracle|SQLServer-->
		<add key="DatabaseType" value="SQLServer"/>

		<!--系统配置-->
		<add key="Title" value="新松自动化物流管理系统(应用服务端) V3.0"/>
		<add key="Language" value="Default"/>

		<!-- 新增HTTP API配置 -->
		<add key="HttpApiUrl" value="http://127.0.0.1:9001"/>
	</appSettings>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2"/></startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="IBatisNet.Common" publicKeyToken="ed781d9fc396c6ca" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-3.0.0.0" newVersion="3.0.0.0"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
