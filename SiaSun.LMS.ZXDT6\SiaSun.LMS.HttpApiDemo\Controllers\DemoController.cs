using System.Web.Http;
using Newtonsoft.Json;

namespace SiaSun.LMS.HttpApiDemo.Controllers
{
    [Route("api/[controller]")]
    public class DemoController : ApiController
    {
        [HttpPost]
        [Route("api/demo/carrier")]
        public IHttpActionResult CarrierRequest([FromBody] object request)
        {
            string result = WcfProxy.CallWcfService("CarrierRequest", request);
            return Ok(JsonConvert.DeserializeObject(result));
        }

        [HttpPost]
        [Route("api/demo/task")]
        public IHttpActionResult TaskRequest([FromBody] object request)
        {
            string result = WcfProxy.CallWcfService("TaskRequest", request);
            return Ok(JsonConvert.DeserializeObject(result));
        }

        [HttpGet]
        [Route("api/demo/status")]
        public IHttpActionResult GetStatus()
        {
            return Ok(new { 
                status = "running", 
                wcfUrl = "http://127.0.0.1:8001/Service/Demo",
                httpApiUrl = "http://127.0.0.1:9001"
            });
        }
    }
}